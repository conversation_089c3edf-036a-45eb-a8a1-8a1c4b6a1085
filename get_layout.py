import layoutparser as lp
import cv2
import os
import numpy as np


def detect_background_color(image, sample_size=50):
    """
    Detect the background color of an image by sampling pixels from the corners.

    Parameters:
    - image: Input image as NumPy array (RGB format)
    - sample_size: Size of the corner regions to sample

    Returns:
    - background_color: RGB tuple representing the most common background color
    """
    height, width = image.shape[:2]

    # Sample from four corners
    corners = [
        image[0:sample_size, 0:sample_size],  # Top-left
        image[0:sample_size, width-sample_size:width],  # Top-right
        image[height-sample_size:height, 0:sample_size],  # Bottom-left
        image[height-sample_size:height, width-sample_size:width]  # Bottom-right
    ]

    # Collect all corner pixels
    corner_pixels = []
    for corner in corners:
        corner_pixels.extend(corner.reshape(-1, corner.shape[-1]))

    corner_pixels = np.array(corner_pixels)

    # Find the most common color (simple approach - use mean)
    background_color = np.mean(corner_pixels, axis=0).astype(int)

    return tuple(background_color)


def is_non_background_pixel(pixel, background_color, threshold=30):
    """
    Check if a pixel is significantly different from the background color.

    Parameters:
    - pixel: RGB pixel value
    - background_color: RGB background color tuple
    - threshold: Color difference threshold

    Returns:
    - bool: True if pixel is not background color
    """
    diff = np.sqrt(np.sum((np.array(pixel) - np.array(background_color)) ** 2))
    return diff > threshold


def extend_figure_block(image, x1, y1, x2, y2, background_color, max_extension=50):
    """
    Extend figure block boundaries if there are non-background pixels at the edges.

    Parameters:
    - image: Input image as NumPy array (RGB format)
    - x1, y1, x2, y2: Original bounding box coordinates
    - background_color: RGB background color tuple
    - max_extension: Maximum pixels to extend in any direction

    Returns:
    - extended_x1, extended_y1, extended_x2, extended_y2: Extended coordinates
    """
    height, width = image.shape[:2]
    extended_x1, extended_y1, extended_x2, extended_y2 = x1, y1, x2, y2

    # Check left edge
    left_edge = image[y1:y2, x1]
    if any(is_non_background_pixel(pixel, background_color) for pixel in left_edge):
        # Extend left
        for i in range(1, min(max_extension + 1, x1 + 1)):
            new_x = x1 - i
            if new_x < 0:
                break
            edge_pixels = image[y1:y2, new_x]
            if not any(is_non_background_pixel(pixel, background_color) for pixel in edge_pixels):
                break
            extended_x1 = new_x

    # Check right edge
    right_edge = image[y1:y2, x2-1]
    if any(is_non_background_pixel(pixel, background_color) for pixel in right_edge):
        # Extend right
        for i in range(1, min(max_extension + 1, width - x2)):
            new_x = x2 + i
            if new_x >= width:
                break
            edge_pixels = image[y1:y2, new_x]
            if not any(is_non_background_pixel(pixel, background_color) for pixel in edge_pixels):
                break
            extended_x2 = new_x + 1

    # Check top edge
    top_edge = image[y1, x1:x2]
    if any(is_non_background_pixel(pixel, background_color) for pixel in top_edge):
        # Extend top
        for i in range(1, min(max_extension + 1, y1 + 1)):
            new_y = y1 - i
            if new_y < 0:
                break
            edge_pixels = image[new_y, x1:x2]
            if not any(is_non_background_pixel(pixel, background_color) for pixel in edge_pixels):
                break
            extended_y1 = new_y

    # Check bottom edge
    bottom_edge = image[y2-1, x1:x2]
    if any(is_non_background_pixel(pixel, background_color) for pixel in bottom_edge):
        # Extend bottom
        for i in range(1, min(max_extension + 1, height - y2)):
            new_y = y2 + i
            if new_y >= height:
                break
            edge_pixels = image[new_y, x1:x2]
            if not any(is_non_background_pixel(pixel, background_color) for pixel in edge_pixels):
                break
            extended_y2 = new_y + 1

    return extended_x1, extended_y1, extended_x2, extended_y2


def draw_layout_boxes(image, layout, box_width=2, with_labels=True, padding=0, extend_figures=True):
    """
    Custom function to draw layout boxes on an image.

    Parameters:
    - image: The original image (as NumPy array)
    - layout: A layoutparser Layout object
    - box_width: Thickness of the bounding box lines
    - with_labels: Whether to draw type labels on boxes
    - padding: Additional padding to add around each bounding box (in pixels)
    - extend_figures: Whether to extend figure blocks based on edge pixel analysis
    """
    image_copy = image.copy()
    image_height, image_width = image.shape[:2]

    # Detect background color once for the entire image
    background_color = detect_background_color(image) if extend_figures else None

    for block in layout:
        x1, y1, x2, y2 = map(int, (block.block.x_1, block.block.y_1, block.block.x_2, block.block.y_2))

        label = block.type if block.type else "Block"

        # For figure blocks, apply extension logic if enabled
        if extend_figures and label.lower() == "figure":
            x1, y1, x2, y2 = extend_figure_block(image, x1, y1, x2, y2, background_color)

        # Apply padding while ensuring we don't go outside image boundaries
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(image_width - 1, x2 + padding)
        y2 = min(image_height - 1, y2 + padding)

        # Choose color by type (optional)
        if label.lower() == "text":
            color = (0, 255, 0)  # Green for text/captions
        elif label.lower() == "figure":
            color = (0, 0, 255)  # Red for figures
        elif label.lower() == "title":
            color = (255, 165, 0)  # Orange for titles
        else:
            color = (255, 0, 0)  # Blue for other types

        # Draw rectangle
        cv2.rectangle(image_copy, (x1, y1), (x2, y2), color, box_width)

        # Put label text
        if with_labels:
            cv2.putText(image_copy, label, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    return image_copy


# def find_caption_blocks(figure_blocks, text_blocks, proximity_threshold=50):
#     figure_and_caption_blocks = []

#     # Add all figure blocks
#     for figure_block in figure_blocks:
#         potential_caption_blocks = [
#             text_block 
#             for text_block in text_blocks 
#             if text_block.block.y_1 > figure_block.block.y_2
#             and abs(((text_block.block.x_1 + text_block.block.x_2) - (figure_block.block.x_1 + figure_block.block.x_2)) / 2) <= proximity_threshold
#         ]

#         caption_block = min(potential_caption_blocks, key=lambda x: x.block.y_1, default=None)
        
#         if caption_block:
#             figure_and_caption_blocks.append([figure_block, caption_block])

#     return figure_and_caption_blocks



def find_caption_blocks(figure_blocks, text_blocks, proximity_threshold=50):
    """
    Find text blocks that are likely captions for figures.
    A text block is considered a caption if it's positioned below a figure
    and within a certain horizontal proximity.

    Parameters:
    - figure_blocks: Layout object containing figure blocks
    - text_blocks: Layout object containing text blocks
    - proximity_threshold: Maximum vertical distance to consider a text as caption

    Returns:
    - Layout object containing figure blocks and their associated caption blocks
    """
    figure_and_caption_blocks = lp.Layout()

    # Add all figure blocks
    for figure_block in figure_blocks:
        figure_and_caption_blocks.append(figure_block)

        # Find potential captions for this figure
        figure_bottom = figure_block.block.y_2
        figure_left = figure_block.block.x_1
        figure_right = figure_block.block.x_2
        figure_center_x = (figure_left + figure_right) / 2

        for text_block in text_blocks:
            text_top = text_block.block.y_1
            text_left = text_block.block.x_1
            text_right = text_block.block.x_2
            text_center_x = (text_left + text_right) / 2

            # Check if text is below the figure
            vertical_distance = text_top - figure_bottom

            # Check if text is horizontally aligned with the figure
            horizontal_overlap = not (text_right < figure_left or text_left > figure_right)

            # Check if text center is reasonably close to figure center
            horizontal_distance = abs(text_center_x - figure_center_x)

            # Consider it a caption if it's below the figure, within proximity threshold, and has some horizontal alignment
            if (0 <= vertical_distance <= proximity_threshold and
                (horizontal_overlap or horizontal_distance <= (figure_right - figure_left) / 2)):
                figure_and_caption_blocks.append(text_block)

    return figure_and_caption_blocks


def extract_layout(image_path, output_path, padding=10, extend_figures=True):
    """
    Extract layout from image and draw bounding boxes with optional padding.

    Parameters:
    - image_path: Path to input image
    - output_path: Path to save output image
    - padding: Additional padding around bounding boxes (default: 10 pixels)
    - extend_figures: Whether to extend figure blocks based on edge pixel analysis (default: True)
    """
    model = lp.Detectron2LayoutModel(
        config_path='lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
        label_map={0: "Text", 1: "Title", 2: "List", 3:"Table", 4:"Figure"},
        device="cpu"
        )
    image = cv2.imread(image_path)

    print(image.shape)

    image = image[..., ::-1]
    layout = model.detect(image)

    # Extract figure and text blocks separately
    figure_blocks = lp.Layout([b for b in layout if b.type and b.type.lower() == "figure"])
    text_blocks = lp.Layout([b for b in layout if b.type and b.type.lower() == "text"])

    # Find figures and their associated captions
    figure_and_caption_blocks = find_caption_blocks(figure_blocks, text_blocks)

    output_image = draw_layout_boxes(image, figure_and_caption_blocks, box_width=2, with_labels=True, padding=padding, extend_figures=extend_figures)

    # Save output to file
    cv2.imwrite(output_path, cv2.cvtColor(output_image, cv2.COLOR_RGB2BGR))
    print(f"[✅] Layout image saved as {output_path} (padding: {padding}px, extend_figures: {extend_figures})")


input_dir = "extracted_images/1502.03167v3.pdf"
output_dir = "layout_images"

os.makedirs(output_dir, exist_ok=True)

start_index, end_index = 1, 11
padding = 10  # Controllable padding in pixels - adjust as needed
extend_figures = True  # Enable figure extension based on edge pixel analysis

for i in range(start_index, end_index + 1):
    extract_layout(f"{input_dir}/page_{i}.png", f"{output_dir}/page_{i}.png", padding=padding, extend_figures=extend_figures)