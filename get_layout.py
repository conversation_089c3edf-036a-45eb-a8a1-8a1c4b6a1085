import layoutparser as lp
import cv2
import os

def draw_layout_boxes(image, layout, box_width=2, with_labels=True, padding=0):
    """
    Custom function to draw layout boxes on an image.

    Parameters:
    - image: The original image (as NumPy array)
    - layout: A layoutparser Layout object
    - box_width: Thickness of the bounding box lines
    - with_labels: Whether to draw type labels on boxes
    - padding: Additional padding to add around each bounding box (in pixels)
    """
    image_copy = image.copy()
    image_height, image_width = image.shape[:2]

    for block in layout:
        x1, y1, x2, y2 = map(int, (block.block.x_1, block.block.y_1, block.block.x_2, block.block.y_2))

        # Apply padding while ensuring we don't go outside image boundaries
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(image_width - 1, x2 + padding)
        y2 = min(image_height - 1, y2 + padding)

        label = block.type if block.type else "Block"

        # Choose color by type (optional)
        if label.lower() == "text":
            color = (0, 255, 0)  # Green for text/captions
        elif label.lower() == "figure":
            color = (0, 0, 255)  # Red for figures
        elif label.lower() == "title":
            color = (255, 165, 0)  # Orange for titles
        else:
            color = (255, 0, 0)  # Blue for other types

        # Draw rectangle
        cv2.rectangle(image_copy, (x1, y1), (x2, y2), color, box_width)

        # Put label text
        if with_labels:
            cv2.putText(image_copy, label, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    return image_copy


# def find_caption_blocks(figure_blocks, text_blocks, proximity_threshold=50):
#     figure_and_caption_blocks = []

#     # Add all figure blocks
#     for figure_block in figure_blocks:
#         potential_caption_blocks = [
#             text_block 
#             for text_block in text_blocks 
#             if text_block.block.y_1 > figure_block.block.y_2
#             and abs(((text_block.block.x_1 + text_block.block.x_2) - (figure_block.block.x_1 + figure_block.block.x_2)) / 2) <= proximity_threshold
#         ]

#         caption_block = min(potential_caption_blocks, key=lambda x: x.block.y_1, default=None)
        
#         if caption_block:
#             figure_and_caption_blocks.append([figure_block, caption_block])

#     return figure_and_caption_blocks



def find_caption_blocks(figure_blocks, text_blocks, proximity_threshold=50):
    """
    Find text blocks that are likely captions for figures.
    A text block is considered a caption if it's positioned below a figure
    and within a certain horizontal proximity.

    Parameters:
    - figure_blocks: Layout object containing figure blocks
    - text_blocks: Layout object containing text blocks
    - proximity_threshold: Maximum vertical distance to consider a text as caption

    Returns:
    - Layout object containing figure blocks and their associated caption blocks
    """
    figure_and_caption_blocks = lp.Layout()

    # Add all figure blocks
    for figure_block in figure_blocks:
        figure_and_caption_blocks.append(figure_block)

        # Find potential captions for this figure
        figure_bottom = figure_block.block.y_2
        figure_left = figure_block.block.x_1
        figure_right = figure_block.block.x_2
        figure_center_x = (figure_left + figure_right) / 2

        for text_block in text_blocks:
            text_top = text_block.block.y_1
            text_left = text_block.block.x_1
            text_right = text_block.block.x_2
            text_center_x = (text_left + text_right) / 2

            # Check if text is below the figure
            vertical_distance = text_top - figure_bottom

            # Check if text is horizontally aligned with the figure
            horizontal_overlap = not (text_right < figure_left or text_left > figure_right)

            # Check if text center is reasonably close to figure center
            horizontal_distance = abs(text_center_x - figure_center_x)

            # Consider it a caption if it's below the figure, within proximity threshold, and has some horizontal alignment
            if (0 <= vertical_distance <= proximity_threshold and
                (horizontal_overlap or horizontal_distance <= (figure_right - figure_left) / 2)):
                figure_and_caption_blocks.append(text_block)

    return figure_and_caption_blocks


def extract_layout(image_path, output_path, padding=10):
    """
    Extract layout from image and draw bounding boxes with optional padding.

    Parameters:
    - image_path: Path to input image
    - output_path: Path to save output image
    - padding: Additional padding around bounding boxes (default: 10 pixels)
    """
    model = lp.Detectron2LayoutModel(
        config_path='lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
        label_map={0: "Text", 1: "Title", 2: "List", 3:"Table", 4:"Figure"},
        device="cpu"
        )
    image = cv2.imread(image_path)
    
    print(image.shape)

    image = image[..., ::-1]
    layout = model.detect(image)

    # Extract figure and text blocks separately
    figure_blocks = lp.Layout([b for b in layout if b.type and b.type.lower() == "figure"])
    text_blocks = lp.Layout([b for b in layout if b.type and b.type.lower() == "text"])

    # Find figures and their associated captions
    figure_and_caption_blocks = find_caption_blocks(figure_blocks, text_blocks)

    output_image = draw_layout_boxes(image, figure_and_caption_blocks, box_width=2, with_labels=True, padding=padding)

    # Save output to file
    cv2.imwrite(output_path, cv2.cvtColor(output_image, cv2.COLOR_RGB2BGR))
    print(f"[✅] Layout image saved as {output_path} (padding: {padding}px)")


input_dir = "extracted_images/1502.03167v3.pdf"
output_dir = "layout_images"

os.makedirs(output_dir, exist_ok=True)

start_index, end_index = 1, 11
padding = 10  # Controllable padding in pixels - adjust as needed

for i in range(start_index, end_index + 1):
    extract_layout(f"{input_dir}/page_{i}.png", f"{output_dir}/page_{i}.png", padding=padding)