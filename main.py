import fitz  # pip install pymupdf
import os
from io import BytesIO
import requests
import numpy as np
import cv2
import layoutparser as lp
import json
from get_layout import extract_layout


def get_pdf_bytes(url: str) -> BytesIO:
    response = requests.get(url)
    response.raise_for_status()

    print(f"=> Downloaded PDF from {url}")

    return BytesIO(response.content)


def get_images(pdf_bytes):
    with fitz.open(stream=pdf_bytes.getvalue(), filetype="pdf") as doc:
        imgs = []
        for page in doc:
            pix = page.get_pixmap(dpi=300)
            raw_bytes = pix.tobytes("png")
            nparr = np.frombuffer(raw_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            imgs.append(img)

        print(f"=> Extracted {len(doc)} images from PDF")

    return imgs


if __name__ == "__main__":
    with open('data.json', 'r') as f:
        data_list = json.load(f)

    for data in data_list[1:2]:
        url = f"https://arxiv.org/pdf/{data['id']}"
        pdf_bytes = get_pdf_bytes(url)
        imgs = get_images(pdf_bytes)

        # Create output dir
        os.makedirs(f"output/{data['id']}", exist_ok=True)

        for i, img in enumerate(imgs):
            extract_layout(img, f"output/{data['id']}/page_{i}.png")